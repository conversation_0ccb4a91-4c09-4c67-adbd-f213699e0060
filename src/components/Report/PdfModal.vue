<template>
  <a-modal v-model:open="visible" :title="title" width="80%" :footer="null" @cancel="onClose">
    <iframe :src="pdfUrl" style="width: 100%; height: 80vh; border: none"></iframe>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';

  const visible = ref(false);
  const pdfUrl = ref('');
  const title = ref('报告');

  const open = (url: string, titleStr: string) => {
    title.value = titleStr;
    visible.value = true;
    // pdfUrl.value = url;
    pdfUrl.value = getFileAccessHttpUrl(url);
  };

  const onClose = () => {
    visible.value = false;
  };

  defineExpose({
    open,
  });
</script>
