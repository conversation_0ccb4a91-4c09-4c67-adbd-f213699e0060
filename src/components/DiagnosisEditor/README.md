# DiagnosisEditor 诊断编辑器组件

DiagnosisEditor 是一个用于编辑多行诊断信息的组件，每行一个诊断，支持拖拽排序、添加、插入和删除操作，并自动维护行号。

## 特性

- 每行一个诊断，基于 ICD10Input 组件实现自由文本输入和搜索建议
- 支持通过拖拽调整诊断顺序
- 支持添加新行、在指定位置插入行和删除行
- 支持按回车键快速添加下一行诊断
- 支持使用上下箭头键在行间快速切换
- 自动删除空行（当行内容清空时自动删除）
- 自动维护行号
- 支持 v-model 双向绑定
- 空列表时自动添加一个空行

## 使用方法

### 基本使用

```vue
<template>
  <DiagnosisEditor v-model="diagnosisList" @change="handleChange" />
</template>

<script setup>
  import { ref } from 'vue';
  import { DiagnosisEditor } from '@/components/DiagnosisEditor';

  const diagnosisList = ref([]);

  const handleChange = (list) => {
    console.log('诊断列表变化:', list);
  };
</script>
```

### 带初始值

```vue
<template>
  <DiagnosisEditor v-model="initialDiagnosisList" @change="handleChange" />
</template>

<script setup>
  import { ref } from 'vue';
  import { DiagnosisEditor } from '@/components/DiagnosisEditor';

  const initialDiagnosisList = ref(['高血压', '2型糖尿病', '冠心病', '高脂血症']);

  const handleChange = (list) => {
    console.log('诊断列表变化:', list);
  };
</script>
```

## 属性

| 属性名     | 类型     | 默认值 | 说明                            |
| ---------- | -------- | ------ | ------------------------------- |
| modelValue | string[] | []     | 诊断列表数据，支持 v-model 绑定 |

## 事件

| 事件名 | 参数             | 说明               |
| ------ | ---------------- | ------------------ |
| change | (list: string[]) | 诊断列表变化时触发 |

## 操作说明

- **拖拽排序**：通过行左侧的拖动图标可以调整诊断顺序
- **添加行**：点击底部"添加诊断"按钮添加新行
- **插入行**：点击行右侧"+"按钮在当前行后插入新行
- **删除行**：点击行右侧删除按钮删除当前行，或者清空行内容后按Delete/Backspace键删除行（包括最末尾的空行）
- **快速添加**：在编辑诊断时，按下回车键可以快速添加下一行诊断并自动聚焦（如果下拉菜单打开，则先选中当前项再添加新行；如果下一行已经存在且为空，则直接聚焦到下一行而不添加新行）
- **行间导航**：光标在行首按上箭头键可移动到上一行的结尾，光标在行尾按下箭头键可移动到下一行的开头；当下拉菜单打开时，向下箭头键用于在候选项中导航，而光标在行首按向上箭头键仍然可以移动到上一行

## 示例

查看 `demo.vue` 文件获取更多使用示例。
