<template>
  <a-modal
    v-model:open="visible"
    title="依赖项目检查"
    width="600px"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :ok-text="okText"
    :cancel-text="cancelText"
  >
    <div class="dependency-check-content">
      <a-alert :message="alertMessage" :description="alertDescription" type="warning" show-icon style="margin-bottom: 20px" />

      <a-list :data-source="missingDependencies" size="small" class="dependency-list">
        <template #renderItem="{ item }">
          <a-list-item class="dependency-item">
            <a-list-item-meta>
              <template #title>
                <span v-if="item.type === 'GROUP'" class="dependency-title">
                  <a-icon type="folder" style="margin-right: 8px; color: #1890ff" />
                  {{ item.name }} (大项)
                </span>
                <span v-else class="dependency-title">
                  <a-icon type="file-text" style="margin-right: 8px; color: #52c41a" />
                  {{ item.groupName }} → {{ item.itemName }} (小项)
                </span>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>

      <div v-if="missingDependencies.length === 0" class="empty-state">
        <a-empty description="所有依赖项目都已完成" />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, computed } from 'vue';

  const visible = ref(false);
  const missingDependencies = ref([]);
  const currentGroup = ref(null);
  const mode = ref('warning'); // 'warning' | 'info'

  const emit = defineEmits(['navigate', 'confirm', 'cancel']);

  // 计算属性
  const alertMessage = computed(() => {
    if (mode.value === 'info') {
      return '依赖项目信息';
    }
    return missingDependencies.value.length > 0 ? '检测到未完成的依赖项目' : '依赖检查通过';
  });

  const alertDescription = computed(() => {
    if (mode.value === 'info') {
      return '以下是当前项目的依赖关系：';
    }
    return missingDependencies.value.length > 0 ? '以下项目需要先录入结果才能继续：' : '所有依赖项目都已完成，可以继续录入。';
  });

  const okText = computed(() => {
    return missingDependencies.value.length > 0 ? '继续保存' : '确定';
  });

  const cancelText = computed(() => {
    return missingDependencies.value.length > 0 ? '取消' : '关闭';
  });

  /**
   * 打开弹窗
   * @param {Object} group 当前项目组
   * @param {Array} missing 缺失的依赖项目
   * @param {string} modalMode 弹窗模式 'warning' | 'info'
   */
  function open(group, missing = [], modalMode = 'warning') {
    currentGroup.value = group;
    missingDependencies.value = missing;
    mode.value = modalMode;
    visible.value = true;
  }

  /**
   * 确认按钮处理
   */
  function handleConfirm() {
    emit('confirm', {
      group: currentGroup.value,
      missing: missingDependencies.value,
      forceConfirm: missingDependencies.value.length > 0,
    });
    visible.value = false;
  }

  /**
   * 取消按钮处理
   */
  function handleCancel() {
    emit('cancel', {
      group: currentGroup.value,
      missing: missingDependencies.value,
    });
    visible.value = false;
  }

  /**
   * 关闭弹窗
   */
  function close() {
    visible.value = false;
  }

  // 暴露方法给父组件
  defineExpose({
    open,
    close,
  });
</script>

<style scoped>
  .dependency-check-content {
    max-height: 350px;
    overflow-y: auto;
    padding: 10px;
  }

  .dependency-list {
    margin: 0;
    padding: 0;
  }

  .dependency-item {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .dependency-item:last-child {
    border-bottom: none;
  }

  .dependency-title {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }

  /* 自定义滚动条样式 */
  .dependency-check-content::-webkit-scrollbar {
    width: 6px;
  }

  .dependency-check-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .dependency-check-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .dependency-check-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 移除默认的列表样式 */
  :deep(.ant-list-item) {
    padding: 0;
    border: none;
  }

  :deep(.ant-list-item-meta) {
    margin-bottom: 0;
  }

  :deep(.ant-list-item-meta-content) {
    flex: 1;
  }
</style>
