# ICD10Input 组件

ICD10Input 是一个用于输入 ICD10 诊断信息的组件，以自由文本输入为主，远程搜索为辅。

## 特性

- 以自由文本输入为主，可直接输入任意诊断信息
- 输入时自动搜索匹配的 ICD10 诊断信息作为辅助
- 支持单选和多选模式
- 多选模式下以标签形式显示已选项
- 支持 v-model 双向绑定

## 使用方法

### 基本使用（自由文本输入）

```vue
<template>
  <ICD10Input v-model="value" @change="handleChange" />
</template>

<script setup>
  import { ref } from 'vue';
  import { ICD10Input } from '@/components/ICD10Input';

  const value = ref('');
  const handleChange = (val) => {
    console.log('输入/选择的值:', val);
  };
</script>
```

### 多选模式

```vue
<template>
  <ICD10Input v-model="values" multiple @change="handleChange" />
</template>

<script setup>
  import { ref } from 'vue';
  import { ICD10Input } from '@/components/ICD10Input';

  const values = ref([]);
  const handleChange = (vals) => {
    console.log('已选择的值:', vals);
  };
</script>
```

### 带初始值

```vue
<template>
  <ICD10Input v-model="value" @change="handleChange" />
</template>

<script setup>
  import { ref } from 'vue';
  import { ICD10Input } from '@/components/ICD10Input';

  // 初始值可以是任意文本
  // 如果是ICD10编码，会自动查询对应的名称
  // 如果是自定义文本，则直接显示

  const value = ref('高血压');
  const handleChange = (val) => {
    console.log('值变化:', val);
  };
</script>
```

## 属性

| 属性名                   | 类型                | 默认值                          | 说明                        |
| ------------------------ | ------------------- | ------------------------------- | --------------------------- |
| modelValue               | String/Number/Array | undefined                       | 组件的值，支持 v-model 绑定 |
| placeholder              | String              | '请输入ICD10诊断信息'           | 输入框占位文本              |
| noMatchText              | String              | '未找到匹配结果'                | 无匹配结果时显示的文本      |
| fieldMapping             | Object              | { value: 'code', text: 'name' } | 字段映射配置                |
| pageSize                 | Number              | 10                              | 每页加载的数据条数          |
| params                   | Object              | {}                              | 额外的请求参数              |
| multiple                 | Boolean             | false                           | 是否启用多选模式            |
| dropdownMatchSelectWidth | Boolean             | true                            | 下拉菜单是否与输入框同宽    |

## 事件

| 事件名 | 参数    | 说明                   |
| ------ | ------- | ---------------------- |
| change | (value) | 输入或选择值变化时触发 |

## 示例

查看 `demo.vue` 文件获取更多使用示例。
