package org.jeecg.modules.fee.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.jeecg.modules.fee.dto.CompanyPaymentQueryDTO;
import org.jeecg.modules.fee.vo.CompanyPaymentStatistics;
import org.jeecg.modules.fee.vo.SubstituteDetail;
import org.jeecg.modules.fee.vo.PaymentRecordDetail;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 团体支付统计分析服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ICompanyPaymentStatisticsService {
    
    /**
     * 获取单位支付统计信息
     * 
     * @param companyRegId 单位预约ID
     * @return 统计信息
     */
    CompanyPaymentStatistics getStatistics(String companyRegId);
    
    /**
     * 分页查询单位支付统计列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<CompanyPaymentStatistics> getStatisticsList(CompanyPaymentQueryDTO queryDTO);
    
    /**
     * 获取替检详情列表
     * 
     * @param companyRegId 单位预约ID
     * @return 替检详情列表
     */
    List<SubstituteDetail> getSubstituteDetails(String companyRegId);
    
    /**
     * 获取支付记录详情列表
     * 
     * @param companyRegId 单位预约ID
     * @return 支付记录详情列表
     */
    List<PaymentRecordDetail> getPaymentRecordDetails(String companyRegId);
    
    /**
     * 获取支付统计摘要
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计摘要
     */
    Object getPaymentSummary(String startDate, String endDate);
    
    /**
     * 导出体检登记名单
     * 
     * @param companyRegId 单位预约ID
     * @param response HTTP响应
     */
    void exportRegistrationList(String companyRegId, HttpServletResponse response);
    
    /**
     * 异步导出体检登记名单
     * 
     * @param exportRequest 导出请求
     * @return 任务信息
     */
    Object asyncExportRegistrationList(Object exportRequest);
    
    /**
     * 批量导出体检登记名单
     * 
     * @param batchExportRequest 批量导出请求
     * @param response HTTP响应
     */
    void batchExportRegistrationList(Object batchExportRequest, HttpServletResponse response);
    
    /**
     * 获取导出任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    Object getExportTaskStatus(String taskId);
    
    /**
     * 取消导出任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelExportTask(String taskId);
    
    /**
     * 下载导出文件
     * 
     * @param taskId 任务ID
     * @param response HTTP响应
     */
    void downloadExportFile(String taskId, HttpServletResponse response);
}
