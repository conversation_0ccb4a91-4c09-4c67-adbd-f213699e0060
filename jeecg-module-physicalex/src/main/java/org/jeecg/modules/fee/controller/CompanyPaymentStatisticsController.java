package org.jeecg.modules.fee.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.fee.dto.CompanyPaymentQueryDTO;
import org.jeecg.modules.fee.service.ICompanyPaymentStatisticsService;
import org.jeecg.modules.fee.vo.CompanyPaymentStatistics;
import org.jeecg.modules.fee.vo.CompanyPaymentStatisticsDetail;
import org.jeecg.modules.fee.vo.SubstituteDetail;
import org.jeecg.modules.fee.vo.PaymentRecordDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 团体支付统计分析控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "团体支付统计分析")
@RestController
@RequestMapping("/fee/companyPaymentStatistics")
@Slf4j
public class CompanyPaymentStatisticsController {
    
    @Autowired
    private ICompanyPaymentStatisticsService statisticsService;
    
    @GetMapping("/list")
    @ApiOperation("获取单位支付统计列表")
    @AutoLog(value = "获取单位支付统计列表")
    public Result<IPage<CompanyPaymentStatistics>> getStatisticsList(CompanyPaymentQueryDTO queryDTO) {
        try {
            log.info("查询单位支付统计列表，查询条件: {}", queryDTO);
            
            IPage<CompanyPaymentStatistics> result = statisticsService.getStatisticsList(queryDTO);
            
            log.info("查询单位支付统计列表完成，总数: {}", result.getTotal());
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("查询单位支付统计列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/detail/{companyRegId}")
    @ApiOperation("获取单位支付统计详情")
    @AutoLog(value = "获取单位支付统计详情")
    public Result<CompanyPaymentStatisticsDetail> getStatisticsDetail(
            @PathVariable String companyRegId) {
        
        try {
            log.info("查询单位支付统计详情，单位ID: {}", companyRegId);
            
            // 1. 基础统计
            CompanyPaymentStatistics statistics = statisticsService.getStatistics(companyRegId);
            
            // 2. 替检详情
            List<SubstituteDetail> substituteDetails = statisticsService.getSubstituteDetails(companyRegId);
            
            // 3. 支付记录详情
            List<PaymentRecordDetail> paymentRecords = statisticsService.getPaymentRecordDetails(companyRegId);
            
            // 4. 组装详情对象
            CompanyPaymentStatisticsDetail detail = CompanyPaymentStatisticsDetail.builder()
                    .statistics(statistics)
                    .substituteDetails(substituteDetails)
                    .paymentRecords(paymentRecords)
                    .build();
            
            log.info("查询单位支付统计详情完成，单位: {}", statistics != null ? statistics.getCompanyName() : "未知");
            return Result.OK(detail);
            
        } catch (Exception e) {
            log.error("查询单位支付统计详情失败，单位ID: {}", companyRegId, e);
            return Result.error("查询详情失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/substitute/{companyRegId}")
    @ApiOperation("获取替检详情列表")
    @AutoLog(value = "获取替检详情列表")
    public Result<List<SubstituteDetail>> getSubstituteDetails(@PathVariable String companyRegId) {
        try {
            log.info("查询替检详情列表，单位ID: {}", companyRegId);
            
            List<SubstituteDetail> result = statisticsService.getSubstituteDetails(companyRegId);
            
            log.info("查询替检详情列表完成，替检人数: {}", result.size());
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("查询替检详情列表失败，单位ID: {}", companyRegId, e);
            return Result.error("查询替检详情失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/payment-records/{companyRegId}")
    @ApiOperation("获取支付记录详情")
    @AutoLog(value = "获取支付记录详情")
    public Result<List<PaymentRecordDetail>> getPaymentRecordDetails(@PathVariable String companyRegId) {
        try {
            log.info("查询支付记录详情，单位ID: {}", companyRegId);
            
            List<PaymentRecordDetail> result = statisticsService.getPaymentRecordDetails(companyRegId);
            
            log.info("查询支付记录详情完成，记录数: {}", result.size());
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("查询支付记录详情失败，单位ID: {}", companyRegId, e);
            return Result.error("查询支付记录失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/summary")
    @ApiOperation("获取支付统计摘要")
    @AutoLog(value = "获取支付统计摘要")
    public Result<Object> getPaymentSummary(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            log.info("查询支付统计摘要，时间范围: {} - {}", startDate, endDate);
            
            Object result = statisticsService.getPaymentSummary(startDate, endDate);
            
            log.info("查询支付统计摘要完成");
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("查询支付统计摘要失败", e);
            return Result.error("查询摘要失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/export/{companyRegId}")
    @ApiOperation("导出体检登记名单")
    @AutoLog(value = "导出体检登记名单")
    public void exportRegistrationList(
            @PathVariable String companyRegId,
            @RequestParam(required = false, defaultValue = "all") String exportType,
            @RequestParam(required = false, defaultValue = "true") Boolean enableMasking,
            @RequestParam(required = false) String selectedFields,
            HttpServletResponse response) {
        
        try {
            log.info("导出体检登记名单，单位ID: {}, 导出类型: {}", companyRegId, exportType);
            
            statisticsService.exportRegistrationList(companyRegId, response);
            
            log.info("导出体检登记名单完成，单位ID: {}", companyRegId);
            
        } catch (Exception e) {
            log.error("导出体检登记名单失败，单位ID: {}", companyRegId, e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }
    
    @PostMapping("/export/async")
    @ApiOperation("异步导出体检登记名单")
    @AutoLog(value = "异步导出体检登记名单")
    public Result<Object> asyncExportRegistrationList(@RequestBody Object exportRequest) {
        try {
            log.info("异步导出体检登记名单，请求: {}", exportRequest);
            
            Object result = statisticsService.asyncExportRegistrationList(exportRequest);
            
            log.info("异步导出任务创建成功");
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("异步导出任务创建失败", e);
            return Result.error("创建导出任务失败：" + e.getMessage());
        }
    }
    
    @PostMapping("/export/batch")
    @ApiOperation("批量导出体检登记名单")
    @AutoLog(value = "批量导出体检登记名单")
    public void batchExportRegistrationList(
            @RequestBody Object batchExportRequest,
            HttpServletResponse response) {
        
        try {
            log.info("批量导出体检登记名单，请求: {}", batchExportRequest);
            
            statisticsService.batchExportRegistrationList(batchExportRequest, response);
            
            log.info("批量导出体检登记名单完成");
            
        } catch (Exception e) {
            log.error("批量导出体检登记名单失败", e);
            throw new RuntimeException("批量导出失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/export/task-status/{taskId}")
    @ApiOperation("获取导出任务状态")
    @AutoLog(value = "获取导出任务状态")
    public Result<Object> getExportTaskStatus(@PathVariable String taskId) {
        try {
            log.info("查询导出任务状态，任务ID: {}", taskId);
            
            Object result = statisticsService.getExportTaskStatus(taskId);
            
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("查询导出任务状态失败，任务ID: {}", taskId, e);
            return Result.error("查询任务状态失败：" + e.getMessage());
        }
    }
    
    @PostMapping("/export/cancel/{taskId}")
    @ApiOperation("取消导出任务")
    @AutoLog(value = "取消导出任务")
    public Result<Boolean> cancelExportTask(@PathVariable String taskId) {
        try {
            log.info("取消导出任务，任务ID: {}", taskId);
            
            boolean result = statisticsService.cancelExportTask(taskId);
            
            log.info("取消导出任务完成，任务ID: {}, 结果: {}", taskId, result);
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("取消导出任务失败，任务ID: {}", taskId, e);
            return Result.error("取消任务失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/export/download/{taskId}")
    @ApiOperation("下载导出文件")
    @AutoLog(value = "下载导出文件")
    public void downloadExportFile(@PathVariable String taskId, HttpServletResponse response) {
        try {
            log.info("下载导出文件，任务ID: {}", taskId);
            
            statisticsService.downloadExportFile(taskId, response);
            
            log.info("下载导出文件完成，任务ID: {}", taskId);
            
        } catch (Exception e) {
            log.error("下载导出文件失败，任务ID: {}", taskId, e);
            throw new RuntimeException("下载文件失败：" + e.getMessage());
        }
    }
}
